#region imports
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using System.Globalization;
    using System.Drawing;
    using QuantConnect;
    using QuantConnect.Algorithm.Framework;
    using QuantConnect.Algorithm.Framework.Selection;
    using QuantConnect.Algorithm.Framework.Alphas;
    using QuantConnect.Algorithm.Framework.Portfolio;
    using QuantConnect.Algorithm.Framework.Portfolio.SignalExports;
    using QuantConnect.Algorithm.Framework.Execution;
    using QuantConnect.Algorithm.Framework.Risk;
    using QuantConnect.Algorithm.Selection;
    using QuantConnect.Api;
    using QuantConnect.Parameters;
    using QuantConnect.Benchmarks;
    using QuantConnect.Brokerages;
    using QuantConnect.Commands;
    using QuantConnect.Configuration;
    using QuantConnect.Util;
    using QuantConnect.Interfaces;
    using QuantConnect.Algorithm;
    using QuantConnect.Indicators;
    using QuantConnect.Data;
    using QuantConnect.Data.Auxiliary;
    using QuantConnect.Data.Consolidators;
    using QuantConnect.Data.Custom;
    using QuantConnect.Data.Custom.IconicTypes;
    using QuantConnect.DataSource;
    using QuantConnect.Data.Fundamental;
    using QuantConnect.Data.Market;
    using QuantConnect.Data.Shortable;
    using QuantConnect.Data.UniverseSelection;
    using QuantConnect.Notifications;
    using QuantConnect.Orders;
    using QuantConnect.Orders.Fees;
    using QuantConnect.Orders.Fills;
    using QuantConnect.Orders.OptionExercise;
    using QuantConnect.Orders.Slippage;
    using QuantConnect.Orders.TimeInForces;
    using QuantConnect.Python;
    using QuantConnect.Scheduling;
    using QuantConnect.Securities;
    using QuantConnect.Securities.Equity;
    using QuantConnect.Securities.Future;
    using QuantConnect.Securities.Option;
    using QuantConnect.Securities.Positions;
    using QuantConnect.Securities.Forex;
    using QuantConnect.Securities.Crypto;
    using QuantConnect.Securities.CryptoFuture;
    using QuantConnect.Securities.IndexOption;
    using QuantConnect.Securities.Interfaces;
    using QuantConnect.Securities.Volatility;
    using QuantConnect.Storage;
    using QuantConnect.Statistics;
    using QCAlgorithmFramework = QuantConnect.Algorithm.QCAlgorithm;
    using QCAlgorithmFrameworkBridge = QuantConnect.Algorithm.QCAlgorithm;
    using Calendar = QuantConnect.Data.Consolidators.Calendar;
#endregion
/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

using System;
using Python.Runtime;
using QuantConnect.Data;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Market;
using QuantConnect.Interfaces;

namespace QuantConnect.Data.Consolidators
{
    /// <summary>
    /// This consolidator extends ClassicRenkoConsolidator to allow dynamic updating of the bar size during runtime.
    /// It maintains the same type (RenkoType.Classic) and behavior as the base ClassicRenkoConsolidator,
    /// but adds the ability to modify the bar size without creating a new consolidator.
    /// </summary>
    public class DynamicClassicRenkoConsolidator : BaseTimelessConsolidator<RenkoBar>, ITargetBarSizeChangeable
    {
        private decimal _barSize;
        private bool _evenBars;
        private decimal? _lastCloseValue;
        private decimal _targetBarSize;

        /// <summary>
        /// Bar being created
        /// </summary>
        protected override RenkoBar CurrentBar { get; set; }

        /// <summary>
        /// Gets the kind of the bar
        /// </summary>
        public RenkoType Type => RenkoType.Classic;

        /// <summary>
        /// Gets a clone of the data being currently consolidated
        /// </summary>
        public override IBaseData WorkingData => CurrentBar?.Clone();

        /// <summary>
        /// Gets <see cref="RenkoBar"/> which is the type emitted in the <see cref="IDataConsolidator.DataConsolidated"/> event.
        /// </summary>
        public override Type OutputType => typeof(RenkoBar);

        /// <summary>
        /// Gets or sets the target bar size for Renko bars produced by this consolidator.
        /// When setting a new value, the current bar (if any) will continue with its original bar size,
        /// but any new bars created after this point will use the new target bar size.
        /// </summary>
        public decimal TargetBarSize
        {
            get => _targetBarSize;
            set
            {
                if (value <= 0 || value < Extensions.GetDecimalEpsilon())
                {
                    throw new ArgumentOutOfRangeException(nameof(value),
                        "RenkoConsolidator bar size must be positive and greater than 1e-28");
                }

                _targetBarSize = value;
                // Note: Any current bar will continue with its existing bar size
                // The new bar size will take effect when the next bar is created
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicClassicRenkoConsolidator"/> class using the specified <paramref name="barSize"/>.
        /// The value selector will by default select <see cref="IBaseData.Value"/>
        /// The volume selector will by default select zero.
        /// </summary>
        /// <param name="barSize">The initial bar size to use for this consolidator</param>
        /// <param name="evenBars">When true, bar open/close will be a multiple of the barSize</param>
        public DynamicClassicRenkoConsolidator(decimal barSize, bool evenBars = true)
            : base()
        {
            EpsilonCheck(barSize);
            _barSize = barSize;
            _targetBarSize = barSize;
            _evenBars = evenBars;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicClassicRenkoConsolidator"/> class.
        /// </summary>
        /// <param name="barSize">The size of each bar in units of the value produced by <paramref name="selector"/></param>
        /// <param name="selector">Extracts the value from a data instance to be formed into a <see cref="RenkoBar"/>.</param>
        /// <param name="volumeSelector">Extracts the volume from a data instance.</param>
        /// <param name="evenBars">When true bar open/close will be a multiple of the barSize</param>
        public DynamicClassicRenkoConsolidator(
            decimal barSize,
            Func<IBaseData, decimal> selector,
            Func<IBaseData, decimal> volumeSelector = null,
            bool evenBars = true)
            : base(selector, volumeSelector)
        {
            EpsilonCheck(barSize);
            _barSize = barSize;
            _targetBarSize = barSize;
            _evenBars = evenBars;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicClassicRenkoConsolidator"/> class.
        /// </summary>
        /// <param name="barSize">The size of each bar in units of the value produced by <paramref name="selector"/></param>
        /// <param name="selector">Extracts the value from a data instance to be formed into a <see cref="RenkoBar"/>.</param>
        /// <param name="volumeSelector">Extracts the volume from a data instance.</param>
        /// <param name="evenBars">When true bar open/close will be a multiple of the barSize</param>
        public DynamicClassicRenkoConsolidator(
            decimal barSize,
            PyObject selector,
            PyObject volumeSelector = null,
            bool evenBars = true)
            : base(selector, volumeSelector)
        {
            EpsilonCheck(barSize);
            _barSize = barSize;
            _targetBarSize = barSize;
            _evenBars = evenBars;
        }

        /// <summary>
        /// Resets the DynamicClassicRenkoConsolidator
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            _lastCloseValue = null;
        }

        /// <summary>
        /// Updates the current RangeBar being created with the given data.
        /// Additionally, if it's the case, it consolidates the current RangeBar
        /// </summary>
        /// <param name="time">Time of the given data</param>
        /// <param name="currentValue">Value of the given data</param>
        /// <param name="volume">Volume of the given data</param>
        protected override void UpdateBar(DateTime time, decimal currentValue, decimal volume)
        {
            CurrentBar.Update(time, currentValue, volume);

            if (CurrentBar.IsClosed)
            {
                _lastCloseValue = CurrentBar.Close;
                OnDataConsolidated(CurrentBar);
                CurrentBar = null;
            }
        }

        /// <summary>
        /// Creates a new bar with the given data, using the current TargetBarSize.
        /// This override ensures that newly created bars use the most recent target bar size.
        /// </summary>
        /// <param name="data">The new data for the bar</param>
        /// <param name="currentValue">The new value for the bar</param>
        /// <param name="volume">The new volume to the bar</param>
        protected override void CreateNewBar(IBaseData data, decimal currentValue, decimal volume)
        {
            var open = _lastCloseValue ?? currentValue;
            if (_evenBars && !_lastCloseValue.HasValue)
            {
                open = Math.Ceiling(open / _targetBarSize) * _targetBarSize;
            }

            CurrentBar = new RenkoBar(data.Symbol, data.Time, _targetBarSize, open, volume);
        }

        private static void EpsilonCheck(decimal barSize)
        {
            if (barSize < Extensions.GetDecimalEpsilon())
            {
                throw new ArgumentOutOfRangeException(nameof(barSize),
                    "RenkoConsolidator bar size must be positve and greater than 1e-28");
            }
        }
    }

    /// <summary>
    /// Provides a type safe wrapper on the DynamicClassicRenkoConsolidator class.
    /// This allows defining selector functions with the real type they'll be receiving.
    /// </summary>
    /// <typeparam name="TInput">The input data type</typeparam>
    public class DynamicClassicRenkoConsolidator<TInput> : DynamicClassicRenkoConsolidator
        where TInput : IBaseData
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicClassicRenkoConsolidator{TInput}"/> class.
        /// </summary>
        /// <param name="barSize">The size of each bar in units of the value produced by <paramref name="selector"/></param>
        /// <param name="selector">Extracts the value from a data instance to be formed into a <see cref="RenkoBar"/>.</param>
        /// <param name="volumeSelector">Extracts the volume from a data instance.</param>
        /// <param name="evenBars">When true bar open/close will be a multiple of the barSize</param>
        public DynamicClassicRenkoConsolidator(
            decimal barSize,
            Func<TInput, decimal> selector,
            Func<TInput, decimal> volumeSelector = null,
            bool evenBars = true)
            : base(barSize, x => selector((TInput)x),
                volumeSelector == null ? (Func<IBaseData, decimal>)null : x => volumeSelector((TInput)x), evenBars)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicClassicRenkoConsolidator{TInput}"/> class using the specified <paramref name="barSize"/>.
        /// The value selector will by default select <see cref="IBaseData.Value"/>
        /// The volume selector will by default select zero.
        /// </summary>
        /// <param name="barSize">The initial bar size to use for this consolidator</param>
        /// <param name="evenBars">When true bar open/close will be a multiple of the barSize</param>
        public DynamicClassicRenkoConsolidator(decimal barSize, bool evenBars = true)
            : this(barSize, x => x.Value, x => 0, evenBars)
        {
        }

        /// <summary>
        /// Updates this consolidator with the specified data.
        /// </summary>
        /// <remarks>
        /// Type safe shim method.
        /// </remarks>
        /// <param name="data">The new data for the consolidator</param>
        public void Update(TInput data)
        {
            base.Update(data);
        }
    }
}
