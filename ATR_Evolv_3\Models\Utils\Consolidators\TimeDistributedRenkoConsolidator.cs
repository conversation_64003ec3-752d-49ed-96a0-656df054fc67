#region imports
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using System.Globalization;
    using System.Drawing;
    using QuantConnect;
    using QuantConnect.Algorithm.Framework;
    using QuantConnect.Algorithm.Framework.Selection;
    using QuantConnect.Algorithm.Framework.Alphas;
    using QuantConnect.Algorithm.Framework.Portfolio;
    using QuantConnect.Algorithm.Framework.Portfolio.SignalExports;
    using QuantConnect.Algorithm.Framework.Execution;
    using QuantConnect.Algorithm.Framework.Risk;
    using QuantConnect.Algorithm.Selection;
    using QuantConnect.Api;
    using QuantConnect.Parameters;
    using QuantConnect.Benchmarks;
    using QuantConnect.Brokerages;
    using QuantConnect.Commands;
    using QuantConnect.Configuration;
    using QuantConnect.Util;
    using QuantConnect.Interfaces;
    using QuantConnect.Algorithm;
    using QuantConnect.Indicators;
    using QuantConnect.Data;
    using QuantConnect.Data.Auxiliary;
    using QuantConnect.Data.Consolidators;
    using QuantConnect.Data.Custom;
    using QuantConnect.Data.Custom.IconicTypes;
    using QuantConnect.DataSource;
    using QuantConnect.Data.Fundamental;
    using QuantConnect.Data.Market;
    using QuantConnect.Data.Shortable;
    using QuantConnect.Data.UniverseSelection;
    using QuantConnect.Notifications;
    using QuantConnect.Orders;
    using QuantConnect.Orders.Fees;
    using QuantConnect.Orders.Fills;
    using QuantConnect.Orders.OptionExercise;
    using QuantConnect.Orders.Slippage;
    using QuantConnect.Orders.TimeInForces;
    using QuantConnect.Python;
    using QuantConnect.Scheduling;
    using QuantConnect.Securities;
    using QuantConnect.Securities.Equity;
    using QuantConnect.Securities.Future;
    using QuantConnect.Securities.Option;
    using QuantConnect.Securities.Positions;
    using QuantConnect.Securities.Forex;
    using QuantConnect.Securities.Crypto;
    using QuantConnect.Securities.CryptoFuture;
    using QuantConnect.Securities.IndexOption;
    using QuantConnect.Securities.Interfaces;
    using QuantConnect.Securities.Volatility;
    using QuantConnect.Storage;
    using QuantConnect.Statistics;
    using QCAlgorithmFramework = QuantConnect.Algorithm.QCAlgorithm;
    using QCAlgorithmFrameworkBridge = QuantConnect.Algorithm.QCAlgorithm;
    using Calendar = QuantConnect.Data.Consolidators.Calendar;
#endregion
/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

using System;
using System.Collections.Generic;
using System.Linq;
using QuantConnect.Data.Market;
using QuantConnect.Interfaces;

namespace QuantConnect.Data.Consolidators
{
    /// <summary>
    /// A Renko consolidator that splits large input bars into multiple Renko bricks.
    /// The first brick is emitted immediately, while subsequent bricks from the same input
    /// are queued and emitted one by one when ProcessPendingBarsNow() is called,
    /// respecting a minimum emission interval.
    /// </summary>
    public class TimeDistributedRenkoConsolidator : DataConsolidator<BaseData>, ITargetBarSizeChangeable
    {
        private decimal _brickSize;
        private decimal _targetBarSize;
        private bool _evenBars;
        private readonly TimeSpan _emissionInterval;

        private RenkoBar _currentBarCandidate; // Represents the current state of Renko bar formation from input data
        private decimal? _lastClosedBrickPrice; // The .Close price of the last fully formed brick (emitted or queued)
        private readonly Queue<RenkoBar> _pendingBars = new Queue<RenkoBar>();
        private DateTime _lastPendingBarEmissionTime; // Timestamp of the last bar emitted by ProcessPendingBarsNow or first bar in Update

        /// <summary>
        /// Gets the type produced by this consolidator.
        /// </summary>
        public override Type OutputType => typeof(RenkoBar);

        /// <summary>
        /// Gets a clone of the data being currently consolidated or the last pending bar.
        /// </summary>
        public override IBaseData WorkingData
        {
            get
            {
                if (_currentBarCandidate != null)
                {
                    return _currentBarCandidate.Clone();
                }
                if (_pendingBars.Any())
                {
                    // Cloning the last pending bar might not be perfectly representative of "working" data
                    // but provides some insight into the queue.
                    return _pendingBars.Last().Clone();
                }
                return null;
            }
        }

        /// <summary>
        /// Gets or sets the target bar size for Renko bars produced by this consolidator.
        /// This value will be used for new bars; any currently forming bar will continue with its original size.
        /// </summary>
        public decimal TargetBarSize
        {
            get => _targetBarSize;
            set
            {
                if (value <= 0)
                {
                    throw new ArgumentOutOfRangeException(nameof(value), "TargetBarSize must be positive.");
                }
                _targetBarSize = value;
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TimeDistributedRenkoConsolidator"/> class.
        /// </summary>
        /// <param name="brickSize">The size of each Renko brick.</param>
        /// <param name="emissionInterval">The minimum time interval between emissions of queued bars.</param>
        /// <param name="evenBars">True if bar open/close values should be multiples of brickSize, false otherwise.</param>
        public TimeDistributedRenkoConsolidator(decimal brickSize, TimeSpan emissionInterval, bool evenBars = true)
        {
            if (brickSize <= 0)
            {
                throw new ArgumentOutOfRangeException(nameof(brickSize), "brickSize must be positive.");
            }
            if (emissionInterval < TimeSpan.Zero) // Allow Zero for immediate processing if desired
            {
                throw new ArgumentOutOfRangeException(nameof(emissionInterval), "emissionInterval cannot be negative.");
            }

            _brickSize = brickSize;
            _targetBarSize = brickSize;
            _emissionInterval = emissionInterval;
            _evenBars = evenBars;
            _lastPendingBarEmissionTime = DateTime.MinValue;
        }

        /// <summary>
        /// Updates this consolidator with the specified data.
        /// </summary>
        /// <param name="data">The new data for the consolidator.</param>
        public override void Update(BaseData data)
        {
            decimal currentValue = data.Price;
            decimal volume = 0;
            if (data is Tick tick) volume = tick.Quantity;
            if (data is TradeBar tradeBar) volume = tradeBar.Volume;

            bool firstBrickInThisUpdate = true;

            if (_currentBarCandidate == null)
            {
                _brickSize = _targetBarSize; // Apply new target size when starting a fresh bar candidate
                decimal openPrice;
                if (_lastClosedBrickPrice.HasValue)
                {
                    openPrice = _lastClosedBrickPrice.Value;
                }
                else
                {
                    openPrice = currentValue;
                    if (_evenBars)
                    {
                        openPrice = Math.Ceiling(openPrice / _brickSize) * _brickSize;
                        // If evenizing makes openPrice further from currentValue than a brick, adjust.
                        // This ensures the first bar isn't immediately "closed" if currentValue is, e.g., 10.05, brickSize 1, evenBars true -> open 11.
                        if (Math.Abs(openPrice - currentValue) >= _brickSize)
                        {
                            openPrice = Math.Floor(currentValue / _brickSize) * _brickSize;
                            if (Math.Abs(openPrice - currentValue) >= _brickSize && currentValue < openPrice) // if current value is still much lower
                            {
                                openPrice = currentValue; // Fallback to non-even if extreme
                            }
                        }
                    }
                }
                _currentBarCandidate = new RenkoBar(data.Symbol, data.Time, _brickSize, openPrice, 0);
            }

            // Update candidate bar properties (High, Low, EndTime, current Close before checking for brick closure)
            _currentBarCandidate.EndTime = data.Time;
            _currentBarCandidate.High = Math.Max(_currentBarCandidate.High, currentValue);
            _currentBarCandidate.Low = Math.Min(_currentBarCandidate.Low, currentValue);
            // Volume for the candidate bar will be set if it closes, or accumulated if it doesn't.
            // For now, let's assume the first brick formed gets the input volume.

            while (true) // Loop to form multiple bricks from one input data
            {
                decimal upperBoundary = _currentBarCandidate.Open + _brickSize;
                decimal lowerBoundary = _currentBarCandidate.Open - _brickSize;
                bool closedThisIteration = false;
                decimal brickClosePrice = _currentBarCandidate.Open; // Default if no closure

                if (_currentBarCandidate.Open < upperBoundary && currentValue >= upperBoundary) // Trend up
                {
                    brickClosePrice = upperBoundary;
                    closedThisIteration = true;
                }
                else if (_currentBarCandidate.Open > lowerBoundary && currentValue <= lowerBoundary) // Trend down
                {
                    brickClosePrice = lowerBoundary;
                    closedThisIteration = true;
                }

                if (closedThisIteration)
                {
                    RenkoBar closedBrick = (RenkoBar)_currentBarCandidate.Clone(); // Clone before modifying for emission
                    closedBrick.Close = brickClosePrice;
                    closedBrick.High = Math.Max(closedBrick.Open, closedBrick.Close); // Ensure H/L are consistent with O/C
                    closedBrick.Low = Math.Min(closedBrick.Open, closedBrick.Close);
                    // Call RenkoBar's own Update method to correctly set IsClosed and other internal states if necessary.
                    // Pass its own EndTime and Close price. Volume for this update call can be 0 as we manage it.
                    closedBrick.Update(closedBrick.EndTime, closedBrick.Close, 0m); 
                    closedBrick.Volume = firstBrickInThisUpdate ? volume : 0; // Only first brick gets volume

                    if (firstBrickInThisUpdate)
                    {
                        OnDataConsolidated(closedBrick);
                        _lastPendingBarEmissionTime = closedBrick.EndTime; // Track emission time
                        firstBrickInThisUpdate = false;
                    }
                    else
                    {
                        _pendingBars.Enqueue(closedBrick);
                    }

                    _lastClosedBrickPrice = closedBrick.Close;

                    // Setup for the next potential brick from the same input data
                    _currentBarCandidate = new RenkoBar(data.Symbol, data.Time, _brickSize, closedBrick.Close, 0);
                    _currentBarCandidate.EndTime = data.Time; // StartTime and EndTime are initially the same
                    // High/Low for the new candidate will be its Open until further data.
                }
                else
                {
                    // Bar did not close, update its current close to currentValue
                    _currentBarCandidate.Close = currentValue;
                    // Accumulate volume if the bar is still open and not yet assigned
                    if (firstBrickInThisUpdate) _currentBarCandidate.Volume += volume;
                    break; // Exit loop, wait for more data
                }
            }
        }

        /// <summary>
        /// Processes a pending Renko bar from the queue if conditions are met.
        /// This method should be called externally, for example, from an algorithm's Update loop.
        /// </summary>
        /// <param name="currentSystemTime">The current system time, used to timestamp the emitted bar.</param>
        public void ProcessPendingBarsNow(DateTime currentSystemTime)
        {
            if (_pendingBars.Count > 0)
            {
                // Ensure we respect the emission interval
                if (currentSystemTime >= _lastPendingBarEmissionTime + _emissionInterval)
                {
                    RenkoBar barToEmit = _pendingBars.Dequeue();
                    
                    // Ensure EndTime is logical
                    DateTime newEndTime = currentSystemTime;
                    if (newEndTime < barToEmit.Time) // EndTime should not be before StartTime
                    {
                        newEndTime = barToEmit.Time;
                    }
                     // Ensure EndTime is after the last emission, respecting interval if possible
                    if (newEndTime < _lastPendingBarEmissionTime + _emissionInterval && _lastPendingBarEmissionTime != DateTime.MinValue)
                    {
                         newEndTime = _lastPendingBarEmissionTime + _emissionInterval;
                    }

                    barToEmit.EndTime = newEndTime;
                    _lastPendingBarEmissionTime = newEndTime; // Update last emission time

                    OnDataConsolidated(barToEmit);
                }
            }
        }

        /// <summary>
        /// Scans this consolidator to see if it should emit a bar due to time passing.
        /// For this consolidator, primary emission of queued bars is handled by <see cref="ProcessPendingBarsNow"/>.
        /// This method can be left empty or implement secondary time-based emission if desired.
        /// </summary>
        /// <param name="currentLocalTime">The current time in the local time zone.</param>
        public override void Scan(DateTime currentLocalTime)
        {
            // base.Scan(currentLocalTime); // Base DataConsolidator Scan is empty.
            // Optionally, could call ProcessPendingBarsNow(currentLocalTime) here if automatic timed release is also desired.
            // However, the primary mechanism is the external call as per user request.
        }

        /// <summary>
        /// Resets the consolidator to its initial state.
        /// </summary>
        public override void Reset()
        {
            base.Reset(); // Resets Consolidated and WorkingData in base
            _currentBarCandidate = null;
            _lastClosedBrickPrice = null;
            _pendingBars.Clear();
            _lastPendingBarEmissionTime = DateTime.MinValue;
            // _targetBarSize remains, _brickSize will be updated from it on next new bar.
        }

        /// <summary>
        /// Disposes of the consolidator.
        /// </summary>
        // Removed Dispose override as base.Dispose() is likely sufficient and not virtual.
        // public override void Dispose()
        // {
        //     base.Dispose(); // Unsubscribes DataConsolidated event
        //     _pendingBars.Clear(); // Clear queue on dispose
        // }
    }
}
