# ATR_Evolv_3 Project Details

## Project Overview

This algorithmic trading strategy implements a trend-following approach using DMI (Directional Movement Index) and ATR (Average True Range) indicators with a flip-flop mechanism to alternate between long and short trading modes. The strategy can trade either the underlying stock or options contracts based on configuration.

## File Organization

```
Project Root (ATR_Evolv_3/)
├── Models/
│   ├── Main.cs                                        # Main algorithm entry point
│   ├── 1_UniverseSelection/                           # Universe selection models
│   ├── 2_Alpha/
│   │   ├── FlipFlopDmiAtrAlphaModel.cs                # Alpha model coordinator, generates insights in Update()
│   │   ├── OptionContractAlphaModel.cs                # Alpha model for options trading, generates insights in Update()
│   │   ├── DmiAtrSignalGenerator.cs                   # Trading signal coordination, sets SignalDirection
│   │   ├── TrailingStopManager.cs                     # Stop management
│   │   └── PositionTransitionHandler.cs               # Position state transitions, sets SignalDirection
│   ├── 3_PortfolioConstruction/
│   │   ├── MarginPortfolioConstructionModel.cs        # (Alternative) Portfolio model for stock trading
│   │   └── MaxAllocationEqualWeightingPortfolioConstructionModel.cs # Custom portfolio model with max allocation and rebalance optimization
│   ├── 4_RiskManagement/                              # Risk management models
│   └── Utils/
│       ├── ChartingService.cs                         # Chart visualization service
│       ├── LogManager.cs                              # Logging service (assuming this is its role)
│       ├── PositionEnums.cs                           # Enum definitions for position management
│       ├── SymbolData.cs                              # Container for symbol-specific data, parameters, and SignalDirection
│       ├── Market/                                    # Custom market data types
│       │   ├── VolumeBar.cs                           # Volume-based bar implementation
│       │   └── DollarVolumeBar.cs                     # Dollar volume-based bar implementation
│       ├── Interfaces/                                # Common interfaces
│       │   └── ITargetBarSizeChangeable.cs            # Interface for types with a settable target bar size
│       └── Consolidators/                             # Custom data consolidators
│           ├── VolumeConsolidator.cs                  # Volume-based consolidator, implements ITargetBarSizeChangeable
│           ├── DollarVolumeConsolidator.cs            # Dollar volume-based consolidator
│           ├── DynamicRenkoConsolidator.cs            # Renko consolidator with updatable bar size, implements ITargetBarSizeChangeable
│           ├── DynamicClassicRenkoConsolidator.cs     # Classic Renko consolidator with updatable bar size, implements ITargetBarSizeChangeable
│           └── TimeDistributedRenkoConsolidator.cs    # Time-distributed Renko consolidator that spreads multiple bars over time, implements ITargetBarSizeChangeable
```

## Core Files and Dependencies

### 1. Main Algorithm Entry Point

**File Path:** `Models/Main.cs`  
**Description:** Initializes the algorithm and all framework components.  
**Dependencies:**
- `Models/2_Alpha/FlipFlopDmiAtrAlphaModel.cs`
- `Models/3_PortfolioConstruction/MaxAllocationEqualWeightingPortfolioConstructionModel.cs`
- QuantConnect's standard execution and risk models

**Key Features:**
- Configurable parameters through `[Parameter]` attributes:
  - Strategy parameters:
    - `ConsolidationMinutes` - Bar timeframe (default: 15 minutes)
    - `AdxPeriod` - Period for ADX calculation (default: 10)
    - `AdxThreshold` - Minimum ADX value for trade entry (default: 20)
    - `AtrPeriod` - Period for ATR calculation (default: 5)
    - `AtrMultiplier` - Multiplier for stop distance (default: 5)
    - `PositionSize` - Max allocation percentage (default: 0.1 = 10%)
    - `InsightResolution` - The resolution used for insight duration (default: Resolution.Minute, matching `ConsolidationMinutes`)
    - `InsightDurationBars` - How many bars (at `InsightResolution`) insights remain active (default calculated to approximate 7 days)
  - Option trading parameters:
    - `TradeUnderlying` - Whether to trade the stock or options (default: true)
    - `MinDaysToExpiration` - Minimum days to expiration for options (default: 7)
    - `MaxDaysToExpiration` - Maximum days to expiration for options (default: 45)
    - `StrikeSelectionMode` - Method to select option strikes (default: 0/ATM)
    - `StrikeOffsetPercent` - Percentage offset for custom strike selection (default: 0.05)
  - Chained Renko consolidation parameters (if `UseChainedRenko` is true):
    - `UseChainedRenko` - Enables/disables chained Renko consolidation (default: false)
    - `RenkoVolumeBarSize` - Default bar size for the Volume Renko consolidator (used if history is unavailable for volume calculation) (default: 100000m)
    - `RenkoPriceBarSize` - Default bar size for the Classic Price Renko consolidator (used as a fallback if percentage or adaptive calculation fails) (default: 0.50m)
    - `VolumeAggregationChunkSize` - Number of 1-minute bars to aggregate for median volume calculation (default: 15)
    - `RenkoPriceBarPercentage` - Percentage of underlying price for dynamic Classic Renko bar size (default: 0.001 for 0.1%). Used when `RenkoPriceMode` is `Percentage`.
    - `RenkoPriceMode` - Determines how the Renko price bar size is calculated (default: 0 for `Fixed`, 1 for `Percentage`, 2 for `AdaptiveRange`). Type `int`.
    - `RenkoAdaptiveRangeLevels` - Specifies how many Renko "bricks" should fit into the calculated average daily range when `RenkoPriceMode` is `AdaptiveRange` (default: 20). Type `int`.
    - `RenkoAdaptiveRangeLookbackDays` - The number of calendar days to look back for calculating the average daily range in `AdaptiveRange` mode (default: 7). Type `int`.
  - Clarifications:
    - `RenkoPriceBarSize` is used when `RenkoPriceMode` is `Fixed`, or as a final fallback in `Percentage` or `AdaptiveRange` mode if dynamic calculations fail.
- Configures charts for price action and indicator visualization with diamond markers for stops
- Conditionally sets up options data when trading options instead of stocks
- Delegates data consolidation management to the alpha model
- Uses the custom `MaxAllocationEqualWeightingPortfolioConstructionModel`
- Initializes the algorithm framework components
- Automatically calculates and sets warmup period from beginning of previous year

### 2. Alpha Model Coordinator

**File Path:** `Models/2_Alpha/FlipFlopDmiAtrAlphaModel.cs`  
**Description:** Coordinates the algorithm components, manages securities and consolidators, and **generates insights in its `Update` method based on `SignalDirection` from `SymbolData`.**
**Dependencies:**
- `Models/Utils/SymbolData.cs` - For indicator data, position tracking, and `SignalDirection`
- `Models/Utils/PositionEnums.cs` - For position state management
- `Models/2_Alpha/DmiAtrSignalGenerator.cs` - For signal generation (setting `SignalDirection`)
- `Models/Utils/ChartingService.cs` - For visualization

**Key Features:**
- Complete management of data consolidation:
  - Conditionally creates and registers consolidators in `OnSecuritiesChanged`:
    - If `_useChainedRenko` is true:
        - A `SequentialConsolidator` (Volume Renko -> Classic Price Renko) is used.
        - The `VolumeRenkoConsolidator`'s bar size is dynamically calculated (both for initial setup and daily reset) based on the median volume of the previous trading day's N-minute bars (where N is `VolumeAggregationChunkSize`). Falls back to `RenkoVolumeBarSize` if history is insufficient.
        - The `ClassicRenkoConsolidator`'s bar size is determined by the `GetCurrentPriceBarSize` method. This method checks the `_renkoPriceBarMode` field:
          - If `Fixed`, it uses the `_renkoPriceBarSize`.
          - If `Percentage`, it calculates `currentPrice * _renkoPriceBarPercentage`, with fallbacks to previous day's close, and then to `_renkoPriceBarSize` if calculations are invalid or price is unavailable.
          - If `AdaptiveRange`, it calculates the bar size based on the average daily range over the previous `_renkoAdaptiveRangeLookbackDays` calendar days, divided by `_renkoAdaptiveRangeLevels`, with a minimum of 0.01 and fallbacks to `_renkoPriceBarSize`.
        - A daily reset mechanism is implemented at midnight (exchange time) to recreate the Renko consolidators with these newly calculated volume and price bar sizes.
    - If `_useChainedRenko` is false: a standard `TradeBarConsolidator` (time-based).
  - Handles consolidator cleanup on security removal.
  - Manages consolidated data updates and indicator synchronization via a dedicated `OnRenkoDataConsolidated` handler.
- Stores the selected `_renkoPriceBarMode` (Fixed, Percentage, or AdaptiveRange).
- Stores the `_renkoAdaptiveRangeLevels` and `_renkoAdaptiveRangeLookbackDays` parameters.
- The `GetCurrentPriceBarSize` method implements the logic to determine the price bar size based on `_renkoPriceBarMode`:
  - If `Fixed`, it uses `_renkoPriceBarSize`.
  - If `Percentage`, it calculates `currentPrice * _renkoPriceBarPercentage`, with fallbacks.
  - If `AdaptiveRange`, it calculates the average daily price range (max minute close - min minute close) of the underlying asset over the previous `_renkoAdaptiveRangeLookbackDays` calendar days. The bar size is then `averageDailyRange / _renkoAdaptiveRangeLevels`. A minimum bar size of 0.01 is enforced. Falls back to `_renkoPriceBarSize` if data is insufficient or calculations are invalid.
- Coordinates the interaction between data updates and signal generation
- Handles warmup-to-live transitions and insight re-emission
- **Generates insights with a short duration (1 bar) in the `Update` method based on the `SignalDirection` stored in `SymbolData`.**
- Thread-safe access to shared data using locks where necessary
- Proper cleanup of resources when securities are removed

### 3. Option Contract Alpha Model

**File Path:** `Models/2_Alpha/OptionContractAlphaModel.cs`
**Description:** Extends the base alpha model with option contract selection and management. **Generates option insights in its `Update` method based on underlying signals and internal state management.**
**Dependencies:**
- `Models/2_Alpha/FlipFlopDmiAtrAlphaModel.cs` - Base alpha model that provides underlying signals
- `QuantConnect.Securities.Option` - For option-specific data and operations

**Key Features:**
- Inherits and extends FlipFlopDmiAtrAlphaModel to leverage underlying signal generation.
- **Refined `Update` method logic:**
  - Performs pre-checks: Ensures market is open, checks if the active contract has data in the current slice (clearing state if not), and cleans up any active insights for symbols missing from the current slice.
  - Handles `Flat` signals by calling `CancelAndRemoveAllOptionInsights`.
  - Handles `Up`/`Down` signals:
    - If already holding the correct contract type (Call for Up, Put for Down), takes no action.
    - If holding the opposite contract type or no contract, calls `CancelAndRemoveAllOptionInsights` and clears the active contract.
    - Attempts to find the best new contract (Call for Up, Put for Down).
    - **Emits an insight ONLY when a new contract is successfully found and activated.**
- Accepts and passes through Renko configuration parameters (including `RenkoVolumeBarSize`, `RenkoPriceBarSize`, `VolumeAggregationChunkSize`, and `RenkoPriceBarPercentage`) to the base `FlipFlopDmiAtrAlphaModel`.
- Implements option contract selection logic (`FindBestOptionContract`) with different modes:
  - ATM (At The Money) - Closest strike to current price
  - ITM (In The Money) - Lower strike for calls, higher for puts
  - OTM (Out of The Money) - Higher strike for calls, lower for puts
  - Custom Offset - Strike at specified percentage away from current price
- **Refined `OnSecuritiesChanged` logic:** Cancels insights for *any* removed option security related to the underlying before potentially clearing the active contract reference.
- Configurable option parameters:
  - Min/max days to expiration
  - Strike selection mode
  - Strike offset percentage (for custom mode)
  - Option to always force new contracts
- Efficiently retrieves option chains from current slice data when available.
- Provides detailed logging for state changes, contract selection, and insight management.
- Manages at most one active option contract (`_activeOptionContract`) at any time for the underlying.
- Includes helper methods for logging (`LogPriceSourceComparison`, `LogAvailableSliceOptions`) and cleanup (`CancelAndRemoveAllOptionInsights`).

### 4. Signal Generator Coordinator

**File Path:** `Models/2_Alpha/DmiAtrSignalGenerator.cs`  
**Description:** A coordinator component that processes market data and delegates to specialized handlers. **Sets the `SignalDirection` in `SymbolData` instead of generating insights.**
**Dependencies:**
- `Models/Utils/SymbolData.cs` - For indicator data, position state, and `SignalDirection`
- `Models/2_Alpha/TrailingStopManager.cs` - For stop management
- `Models/2_Alpha/PositionTransitionHandler.cs` - For position transitions

**Key Features:**
- Extracts and processes market data from indicators
- Delegates stop management to TrailingStopManager
- Delegates position transitions (which set `SignalDirection`) to PositionTransitionHandler
- Handles high-level coordination of the trading strategy
- Determines which specialized handler to invoke based on market conditions
- Stores a reference to the `QCAlgorithm` instance for logging and other algorithm-level operations
- Uses strategy parameters stored in SymbolData rather than passing them as method parameters

### 5. Trailing Stop Manager

**File Path:** `Models/2_Alpha/TrailingStopManager.cs`  
**Description:** Specialized component for managing trailing stops.  
**Dependencies:** None external

**Key Features:**
- Calculates and updates trailing stops for both long and short positions
- Implements one-way trailing stops (long stops only move up, short stops only move down)
- Resets and recalculates stops after direction changes
- Updates the active stop value based on current position mode
- Logs stop changes and movements
- Designed with flexibility to support variable multiplier values

### 6. Position Transition Handler

**File Path:** `Models/2_Alpha/PositionTransitionHandler.cs`  
**Description:** Specialized component for managing position state transitions. **Sets the `SignalDirection` in `SymbolData` based on transitions.**
**Dependencies:**
- `Models/2_Alpha/TrailingStopManager.cs` - For stop reset and recalculation

**Key Features:**
- Manages transitions between different position states (Flat, Long, Short)
- Handles the flip-flop logic when stops are violated
- **Sets the `SignalDirection` property (-1, 0, 1) in the `SymbolData` object based on position changes.**
- Implements entry logic based on ADX and DI conditions
- Logs detailed position transition events
- Coordinates with TrailingStopManager for stop calculations
- Stores a reference to the `QCAlgorithm` instance for logging and other algorithm-level operations
- Uses AdxThreshold, AtrMultiplier directly from SymbolData instances.

### 7. Charting Service

**File Path:** `Models/Utils/ChartingService.cs`  
**Description:** Handles all visualization aspects of the strategy.  
**Dependencies:** None external

**Key Features:**
- Plots price data and indicators values
- Visualizes trailing stops based on current position mode stored in SymbolData
- Provides safe plotting with error handling
- Centralizes all chart-related functionality
- Directly accesses position mode from SymbolData rather than having it passed as a parameter
- Uses diamond markers for trailing stops (green for long mode, red for short mode)

### 8. Position State Enumerations

**File Path:** `Models/Utils/PositionEnums.cs`  
**Description:** Contains enum definitions used throughout the project.  

**Key Definitions:**
- `PositionMode` enum:
  - `LongMode` - Strategy is searching for long entries
  - `ShortMode` - Strategy is searching for short entries
- `PositionState` enum:
  - `Flat` - No current position
  - `Long` - Currently in a long position
  - `Short` - Currently in a short position
- `RenkoPriceBarMode` enum:
  - `Fixed` - Renko bar size is a fixed decimal value.
  - `Percentage` - Renko bar size is a percentage of the underlying asset's price.
  - `AdaptiveRange` - Renko bar size is based on the average daily range over a lookback period, divided by a specified number of levels.

### 9. Symbol Data Container

**File Path:** `Models/Utils/SymbolData.cs`  
**Description:** Container class for symbol-specific data, indicators, position tracking, and signal direction.
**Dependencies:** `Models/Utils/PositionEnums.cs`

**Key Features:**
- Manages ADX and ATR indicators for each symbol
- Tracks position state, mode, and trailing stop values
- Maintains separate trailing stops for long and short modes
- Implements thread-safe indicator updates using locks
- Validates input data and provides error checking in constructor:
  - Validates symbol is not null
  - Validates indicator periods are positive
  - Handles parameter validation for all inputs
- Stores the current price and last observed bar data
- Stores strategy parameters (AdxThreshold, AtrMultiplier, `InsightResolution`, `InsightDurationBars`) centralizing parameter management
- **Includes `SignalDirection` property (int: -1, 0, 1) to store the latest signal direction.**
- Acts as a single source of truth for all symbol-specific data and configuration

### 10. Custom Volume-Based Consolidation Components

#### 10.1 VolumeBar

**File Path:** `Models/Utils/Market/VolumeBar.cs`  
**Description:** A data bar that closes when a specified amount of volume has been traded, regardless of the time elapsed.  
**Dependencies:** `TradeBar` from QuantConnect.Data.Market

**Key Features:**
- Tracks OHLC prices and volume
- Closes when the volume reaches the specified bar size (`BarSize`)
- Contains a `IsClosed` property that returns true when volume reaches the bar size
- Provides an `Update` method that returns excess volume when the bar is filled
- Includes a `Rollover` method to create a new bar carrying over the closing price as the new open price
- Extends `TradeBar` to maintain compatibility with existing indicators and consolidators

#### 10.2 DollarVolumeBar

**File Path:** `Models/Utils/Market/DollarVolumeBar.cs`  
**Description:** Extends `VolumeBar` to track dollar volume (price * volume) rather than raw share volume.  
**Dependencies:** `VolumeBar` from Models.Utils.Market

**Key Features:**
- Inherits all functionality from `VolumeBar`
- Additionally tracks dollar volume (price * volume) through a `DollarVolume` property
- Updates dollar volume calculation when the bar is updated
- Useful for comparing trading activity across securities with different price levels

#### 10.3 VolumeConsolidator

**File Path:** `Models/Utils/Consolidators/VolumeConsolidator.cs`  
**Description:** A consolidator that processes incoming market data and emits `VolumeBar` instances when the volume threshold is reached. Implements `ITargetBarSizeChangeable`.
**Dependencies:** `DataConsolidator<BaseData>` from QuantConnect.Data.Consolidators, `ITargetBarSizeChangeable` from Models.Utils.Interfaces

**Key Features:**
- Processes both `TradeBar` and `Tick` data inputs
- Maintains the current working bar and allows dynamic updates to its target size via the `TargetBarSize` property.
- Implements `ITargetBarSizeChangeable` for standardized dynamic bar size updates.
- Automatically handles rollover to new bars when the volume threshold is exceeded
- Configurable tick filtering to handle suspicious price movements while preserving volume
- Customizable through the `AdjustVolume` method which can be overridden in derived classes
- Emits completed bars through a `DataConsolidated` event
- Provides methods to enable/disable tick filtering and set filtering parameters
- Handles the case where a bar is exactly filled (volume equals bar size) by emitting the bar and creating a new one on next data point

#### 10.4 DollarVolumeConsolidator

**File Path:** `Models/Utils/Consolidators/DollarVolumeConsolidator.cs`  
**Description:** Extends `VolumeConsolidator` to create bars based on dollar volume thresholds rather than raw volume.  
**Dependencies:** `VolumeConsolidator` from Models.Utils.Consolidators

**Key Features:**
- Inherits core functionality from `VolumeConsolidator`
- Overrides the `AdjustVolume` method to multiply raw volume by price, converting it to dollar volume
- Creates bars that represent consistent dollar value traded rather than share count
- Particularly useful for comparing trading activity across securities with different price levels
- Helps normalize bars across high-priced and low-priced securities

### 10.5 TimeDistributedRenkoConsolidator

**File Path:** `Models/Utils/Consolidators/TimeDistributedRenkoConsolidator.cs`  
**Description:** Time-distributed Renko consolidator that spreads multiple bars over time to avoid visual overlap in charts. Implements `ITargetBarSizeChangeable`.
**Dependencies:** `RenkoConsolidator` from QuantConnect.Data.Consolidators, `ITargetBarSizeChangeable` from Models.Utils.Interfaces

**Key Features:**
- Queues bars after the first one instead of emitting all immediately
- Uses configurable time intervals (default: 500ms) to spread bars over time
- Updates each queued bar's timestamp to be incrementally spaced
- Preserves all standard Renko behavior for bar creation and price level detection
- Configurable emission interval, defaulting to 500ms between bars
- Supports dynamic bar size updates at runtime via `ITargetBarSizeChangeable`
- Immediate emission of the first bar in a sequence for responsiveness
- Automatic processing of pending bars based on time using the `Scan()` method
- Complete compatibility as a drop-in replacement for standard Renko consolidators

### 11. Portfolio Construction Models

#### 11.1 Max Allocation Equal Weighting Portfolio Construction Model

**File Path:** `Models/3_PortfolioConstruction/MaxAllocationEqualWeightingPortfolioConstructionModel.cs`
**Description:** Custom portfolio construction model that extends `EqualWeightingPortfolioConstructionModel` to limit total portfolio allocation based on the `PositionSize` parameter. **Includes an optimization to avoid duplicate targets.**

**Key Features:**
- Inherits from `EqualWeightingPortfolioConstructionModel`.
- Overrides `DetermineTargetPercent` to scale down the target percentages based on `_maxAllocationPercentage`.
- Ensures the total portfolio allocation does not exceed the configured `PositionSize`.
- Maintains equal weighting for the allocated portion.
- **Overrides `ShouldCreateTargetForInsight` with an `_optimizeRebalancing` flag (default true) to prevent creating new targets if an active insight with the same symbol, direction, and weight already exists.**

#### 11.2 Margin Portfolio Construction Model (Alternative)

**File Path:** `Models/3_PortfolioConstruction/MarginPortfolioConstructionModel.cs`
**Description:** (Alternative) Custom portfolio construction model for stock trading with position sizing.

**Key Features:**
- Configurable position sizing for both long and short positions
- Converts alpha model insights into concrete portfolio targets
- Implements the following logic:
  - For Up (long) insights: Uses +positionSize allocation
  - For Down (short) insights: Uses -positionSize allocation
  - For Flat insights: Sets 0% allocation to close position

## Data Flow and Execution

1. `Models/Main.cs` initializes the algorithm and configuration parameters
2. Based on the `TradeUnderlying` flag, it sets up either equity trading or options trading
3. All parameters, including `PositionSize`, `InsightResolution`, `InsightDurationBars`, and Renko settings, are passed to the alpha model during construction
4. The alpha model creates `SymbolData` instances for each security, storing all parameters
5. The alpha model creates and manages consolidators for each security, which can be either time-based or a chained Renko setup based on configuration:
   - Registers consolidators during `OnSecuritiesChanged`
   - Unregisters them when securities are removed
6. Raw market data is consolidated using one of the following methods:
    - Into time-based bars (e.g., 15-minute) if `UseChainedRenko` is false.
    - Through a Volume Renko consolidator (with a dynamically calculated bar size based on previous day's median N-minute volume) followed by a `ClassicPriceRenkoConsolidator` whose bar size is determined based on the `RenkoPriceMode` (either fixed via `RenkoPriceBarSize`, dynamically calculated as a percentage of current/historical price via `RenkoPriceBarPercentage`, or dynamically calculated based on average daily range and `RenkoAdaptiveRangeLevels` via `AdaptiveRange` mode, all with fallbacks) if `UseChainedRenko` is true. These dynamic sizings occur at initial setup and are updated daily.
    - Alternatively, data can be consolidated using the new `VolumeConsolidator` or `DollarVolumeConsolidator` to create volume-based bars that close when a specific volume or dollar volume threshold is reached rather than after a fixed time period.
    - Or using the `TimeDistributedRenkoConsolidator` to create Renko bars that are distributed over time to avoid overlap.
7. Consolidated bars update indicators in `SymbolData.cs` using thread-safe methods
8. The data flow continues as follows:
   - `Models/2_Alpha/FlipFlopDmiAtrAlphaModel.cs` receives the updated data and delegates to its components
   - `Models/Utils/ChartingService.cs` visualizes the price and indicator data using data from SymbolData
   - `Models/2_Alpha/DmiAtrSignalGenerator.cs` evaluates the data and determines which action to take
   - Signal generation occurs based on the DMI+ATR strategy, **setting the `SignalDirection` in `SymbolData`**
9. **The `Update` method in `FlipFlopDmiAtrAlphaModel` reads the `SignalDirection` from `SymbolData` and creates base `Insight` objects.**
10. **If trading options, the `Update` method in `OptionContractAlphaModel` processes these base signals, manages the active option contract state, performs cleanups, selects new contracts if needed, and emits option-specific `Insight` objects (typically only when activating a new contract).**
11. These insights (either base stock insights or derived option insights) are emitted to the algorithm framework.
12. Based on trading mode:
    - If `TradeUnderlying = true`: `Models/3_PortfolioConstruction/MaxAllocationEqualWeightingPortfolioConstructionModel.cs` converts stock insights to portfolio targets, respecting the `PositionSize` limit and potentially optimizing rebalancing via `ShouldCreateTargetForInsight`.
    - If `TradeUnderlying = false`: `Models/3_PortfolioConstruction/MaxAllocationEqualWeightingPortfolioConstructionModel.cs` converts option insights to portfolio targets, respecting `PositionSize` and potentially optimizing rebalancing.
12. Standard QuantConnect execution model executes the trades

## Charts and Visualization

The algorithm provides two main charts:
1. **Price and Stops**
   - Price action as candlesticks
   - Active trailing stop (color-coded by position mode):
     - Long trailing stops (green diamonds) when in Long Mode
     - Short trailing stops (red diamonds) when in Short Mode

2. **ADX, DI, and ATR**
   - ADX line
   - +DI line
   - -DI line
   - ATR line

3. **Option Analytics** (only when trading options)
   - IV (Implied Volatility)
   - Delta
   - Gamma
   - Theta

## Trailing Stop Mechanism

The trailing stop mechanism implements these key features:

1. **Dual Stop Tracking**: Both long and short trailing stops are calculated and maintained continuously, regardless of the current position or mode.

2. **Immediate Direction Flipping**: When price crosses the active trailing stop level, the system immediately flips from one mode to another without delay.

3. **Stop Reset on Direction Change**: When changing direction due to a stop violation, both trailing stops are reset and recalculated based on the current market conditions.

4. **Autonomous Operation**: The trailing stop calculations proceed continuously without being limited by position status, signal waiting, or bar boundaries.

5. **Visualization**: Only the active trailing stop is plotted as a diamond marker with color-coding (green for long mode, red for short mode) to provide clear visual indication of the current mode.

## Options Trading Features

The algorithm can now trade in two distinct modes:

1. **Stock Trading Mode** (`TradeUnderlying = true`)
   - Trades the underlying stock directly
   - Uses `MarginPortfolioConstructionModel` for position sizing
   - Simpler portfolio management with direct long/short positions

2. **Options Trading Mode** (`TradeUnderlying = false`)
   - Trades options contracts instead of the underlying
   - Uses `OptionsPortfolioConstructionModel` for option contract selection and position sizing
   - Configurable parameters:
     - Strike selection method (ATM, ITM, OTM, Custom)
     - Expiration date range (min/max days to expiration)
     - Position size as percentage of portfolio

3. **Signal Generation Independence**
   - The alpha model generates the same signals based on the underlying price regardless of trading mode
   - The portfolio construction model translates these signals differently based on the trading mode
   - For stock mode: Direct long/short positions in the stock
   - For options mode: Call options for long signals, put options for short signals

4. **Option Selection Logic**
   - Options are filtered by expiration date range
   - Strike prices are selected based on the chosen strike selection mode
   - Option chains are filtered by option right (call/put) based on the signal direction
   - For up signals: Calls are selected
   - For down signals: Puts are selected

## Volume-Based Consolidation Features

The algorithm now includes support for volume-based consolidation as an alternative to time-based or Renko-based consolidation. This approach offers several advantages:

1. **Volume Normalization**: 
   - Volume-based bars represent consistent trading activity rather than arbitrary time intervals
   - Periods with high trading activity create more bars, while quiet periods create fewer bars
   - This prevents dilution of significant price moves in low-volume periods and over-emphasis on noise in high-volume periods

2. **Market Participation Representation**:
   - Each bar represents the same level of market participation in terms of shares traded or dollar value traded
   - Helps identify price levels where significant trading occurred, potentially revealing important support/resistance levels

3. **Filtering Capabilities**:
   - The consolidators include built-in capabilities for filtering suspicious ticks in tick data
   - Volume data is preserved even when price data appears suspicious
   - Configurable percentage-based deviation threshold for identifying suspicious prices

4. **Flexibility**:
   - Can process both TradeBar and Tick data inputs
   - Supports both raw volume and dollar volume consolidation
   - Easily extendable through inheritance for custom volume calculation methods

5. **Integration**:
   - Compatible with existing indicators that accept TradeBar data
   - Can be used with the existing QuantConnect subscription and consolidation framework

These components enhance the strategy's ability to respond to actual trading activity rather than arbitrary time intervals, potentially improving signal quality and reducing noise in highly volatile or inconsistently liquid markets.

## Dynamic Renko Consolidation Features

The algorithm now includes support for dynamic Renko consolidation through two specialized consolidators that extend the standard Lean Renko consolidators:

1. **DynamicRenkoConsolidator**:
   - Extends the standard `RenkoConsolidator` that produces RenkoType.Wicked bars.
   - Implements `ITargetBarSizeChangeable`.
   - Allows updating the bar size during runtime via the `TargetBarSize` property.
   - Maintains compatibility with all existing Renko features.
   - Ensures proper bar formation with dynamically changing bar sizes.

2. **DynamicClassicRenkoConsolidator**:
   - Extends the `BaseTimelessConsolidator<RenkoBar>` just like the original ClassicRenkoConsolidator.
   - Implements `ITargetBarSizeChangeable`.
   - Produces RenkoType.Classic bars with the ability to update the bar size.
   - Preserves the exact behavior of the original ClassicRenkoConsolidator.
   - Adds the `TargetBarSize` property for dynamic bar size adjustment.

3. **Key Advantages**:
   - **Runtime Adaptability**: Bar sizes can be adjusted based on market conditions without creating new consolidators
   - **Consistent API**: Uses the same familiar API as the VolumeConsolidator's TargetBarSize property
   - **Seamless Substitution**: Can directly replace existing Renko consolidators with minimal code changes
   - **Adaptive Sizing**: Enables advanced features like volatility-based or price-percentage-based brick sizing that can be updated throughout the trading day

4. **Usage Examples**:
   - Increase brick size in high volatility conditions to reduce noise
   - Decrease brick size in low volatility conditions for more granular signals
   - Implement adaptive strategies that scale brick size with ATR or other volatility measures
   - Maintain a constant percentage of price as brick size for assets with changing price levels
   
5. **Implementation Details**:
   - Maintains proper handling of _lastCloseValue in Classic mode
   - Updates internal bar size while preserving the current bar's integrity
   - Ensures completed bars use consistent sizing while new bars adapt to updated sizes
   - Properly handles resets and state management

These components extend the existing Renko implementation with dynamic bar sizing capabilities, providing more flexible and adaptable technical analysis tools that can respond to changing market conditions.

## TimeDistributedRenkoConsolidator Enhancement

### Problem Solved
Standard Renko consolidators (including DynamicRenkoConsolidator) emit multiple bars at the same timestamp when large price movements occur. This causes visual overlap in charts where only the latest bar is visible, ruining the Renko visualization.

### Solution Approach
The `TimeDistributedRenkoConsolidator` solves this by:

1. **Queue-Based Emission**: Instead of emitting all bars immediately, it queues bars after the first one
2. **Time Distribution**: Uses configurable time intervals (default: 500ms) to spread bars over time
3. **Timestamp Adjustment**: Updates each queued bar's timestamp to be incrementally spaced
4. **Maintains Renko Logic**: Preserves all standard Renko behavior for bar creation and price level detection

### Key Features
- **Configurable Emission Interval**: Default 500ms between bars, customizable via constructor
- **Dynamic Bar Size Support**: Implements `ITargetBarSizeChangeable` for runtime bar size updates
- **Immediate First Bar**: The first bar in a sequence is emitted immediately for responsiveness
- **Automatic Processing**: Uses the `Scan()` method to process pending bars based on time
- **Complete Compatibility**: Drop-in replacement for standard Renko consolidators

### Usage
```csharp
// Create with default 500ms emission interval
var consolidator = new TimeDistributedRenkoConsolidator(barSize);

// Or specify custom emission interval
var consolidator = new TimeDistributedRenkoConsolidator(barSize, TimeSpan.FromMilliseconds(250));
```

### Benefits
- **Clean Chart Visualization**: Each Renko bar appears at distinct timestamps
- **Preserved Renko Logic**: Maintains correct price levels and bar creation rules
- **Improved Chart Readability**: No overlapping bars in high-volatility periods
- **Real-time Responsiveness**: First bar emits immediately, subsequent bars follow at intervals

## Consolidator Bar Size Management Optimization

The algorithm implements a robust and flexible approach for dynamic Renko consolidators using the `ITargetBarSizeChangeable` interface:

1. **Recursive Consolidator Handling**:
   - Uses a recursive algorithm to traverse and update any depth of nested SequentialConsolidators
   - Can handle arbitrary chaining of consolidators as long as each implements `ITargetBarSizeChangeable`
   - Each consolidator receives the appropriate bar size based on its concrete type

2. **Type-Specific Bar Size Application**:
   - VolumeConsolidator receives volume-based bar size
   - DollarVolumeConsolidator receives dollar volume-based bar size
   - DynamicRenkoConsolidator, DynamicClassicRenkoConsolidator, and TimeDistributedRenkoConsolidator receive price-based bar size
   - All bar sizes are calculated upfront before any updates occur

3. **Implementation Details**:
   - Separates the consolidator traversal logic into a dedicated recursive method 
   - Reports failures at the individual consolidator level
   - Provides detailed logging about which consolidators are being updated and with what values

4. **Benefits**:
   - Supports arbitrary consolidator chaining with any number of layers
   - Eliminates the need for specialized handling of specific consolidator chains
   - Makes the system more future-proof when adding new types of consolidators
   - Maintains the same logical approach for bar size calculation with a more flexible implementation
