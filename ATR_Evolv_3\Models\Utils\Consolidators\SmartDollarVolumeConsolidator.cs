/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#region imports
using System;
using QuantConnect.Data;
using QuantConnect.Data.Market;
#endregion

namespace QuantConnect.Algorithm.CSharp
{
    /// <summary>
    /// Smart DollarVolumeConsolidator that automatically adjusts its target based on input data type.
    /// When receiving RenkoBars (from a previous consolidator), it uses a much smaller target.
    /// </summary>
    public class SmartDollarVolumeConsolidator : DollarVolumeConsolidator
    {
        private readonly decimal _originalTarget;
        private readonly decimal _renkoMultiplier;
        private bool _hasAdjustedForRenko = false;

        /// <summary>
        /// Initializes a new instance of SmartDollarVolumeConsolidator.
        /// </summary>
        /// <param name="barSize">The dollar volume size for raw TradeBars</param>
        /// <param name="renkoMultiplier">Multiplier to apply when receiving RenkoBars (default: 0.01 = 1%)</param>
        public SmartDollarVolumeConsolidator(decimal barSize, decimal renkoMultiplier = 0.01m) : base(barSize)
        {
            _originalTarget = barSize;
            _renkoMultiplier = renkoMultiplier;
            LogManager.Log($"[SMART-DOLLAR] Initialized with originalTarget={_originalTarget:F0}, renkoMultiplier={_renkoMultiplier}");
        }

        /// <summary>
        /// Updates this consolidator with the specified data, auto-adjusting target for RenkoBars
        /// </summary>
        public override void Update(BaseData data)
        {
            // Auto-adjust target on first RenkoBar received
            if (!_hasAdjustedForRenko && data is RenkoBar)
            {
                _hasAdjustedForRenko = true;
                var newTarget = _originalTarget * _renkoMultiplier;
                LogManager.Log($"[SMART-DOLLAR] First RenkoBar detected! Adjusting target from {TargetBarSize:F0} to {newTarget:F0}");
                TargetBarSize = newTarget;
            }

            base.Update(data);
        }
    }
}
