/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

#region imports
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Globalization;
using System.Drawing;
using QuantConnect;
using QuantConnect.Algorithm.Framework;
using QuantConnect.Algorithm.Framework.Selection;
using QuantConnect.Algorithm.Framework.Alphas;
using QuantConnect.Algorithm.Framework.Portfolio;
using QuantConnect.Algorithm.Framework.Execution;
using QuantConnect.Algorithm.Framework.Risk;
using QuantConnect.Algorithm.Selection;
using QuantConnect.Parameters;
using QuantConnect.Benchmarks;
using QuantConnect.Brokerages;
using QuantConnect.Util;
using QuantConnect.Interfaces;
using QuantConnect.Algorithm;
using QuantConnect.Indicators;
using QuantConnect.Data;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Custom;
using QuantConnect.DataSource;
using QuantConnect.Data.Fundamental;
using QuantConnect.Data.Market;
using QuantConnect.Data.UniverseSelection;
using QuantConnect.Notifications;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Orders.Fills;
using QuantConnect.Orders.Slippage;
using QuantConnect.Scheduling;
using QuantConnect.Securities;
using QuantConnect.Securities.Equity;
using QuantConnect.Securities.Future;
using QuantConnect.Securities.Option;
using QuantConnect.Securities.Forex;
using QuantConnect.Securities.Crypto;
using QuantConnect.Securities.Interfaces;
using QuantConnect.Storage;
using QCAlgorithmFramework = QuantConnect.Algorithm.QCAlgorithm;
using QCAlgorithmFrameworkBridge = QuantConnect.Algorithm.QCAlgorithm;
using QuantConnect.Indicators.CandlestickPatterns;
using QLNet;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using System.Text;
#endregion

namespace QuantConnect.Algorithm.CSharp
{    /// <summary>
    /// This consolidator can transform a stream of <see cref="BaseData"/> instances into a stream of <see cref="VolumeBar"/>
    /// with a constant volume for each bar.
    /// </summary>
    public class VolumeConsolidator : DataConsolidator<BaseData>, ITargetBarSizeChangeable
    {
        private VolumeBar _currentBar;
        private decimal _consolidatorTargetBarSize; // Renamed from _barSize
        private readonly TickFilter _tickFilter;
        private readonly bool _tickFilteringEnabled = false;

        /// <summary>
        /// Gets a clone of the data being currently consolidated
        /// </summary>
        public override IBaseData WorkingData => _currentBar;

        /// <summary>
        /// Gets <see cref="VolumeBar"/> which is the type emitted in the <see cref="IDataConsolidator.DataConsolidated"/> event.
        /// </summary>
        public override Type OutputType => typeof(VolumeBar);

        /// <summary>
        /// Event handler that fires when a new piece of data is produced
        /// </summary>
        public new event EventHandler<VolumeBar> DataConsolidated;

        /// <summary>
        /// Initializes a new instance of the <see cref="VolumeConsolidator"/> class using the specified <paramref name="barSize"/>.
        /// </summary>
        /// <param name="barSize">The constant volume size of each bar</param>
        public VolumeConsolidator(decimal barSize)
        {
            if (barSize <= 0) throw new ArgumentOutOfRangeException(nameof(barSize), "barSize must be positive.");
            _consolidatorTargetBarSize = barSize;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="VolumeConsolidator"/> with price deviation filtering.
        /// </summary>
        /// <param name="barSize">The constant volume size of each bar</param>
        /// <param name="maxPriceDeviation">Maximum allowed price deviation (as a decimal percentage) before considering a tick suspicious</param>
        public VolumeConsolidator(decimal barSize, decimal maxPriceDeviation) : this(barSize) // 'this(barSize)' will call the updated constructor
        {
            _tickFilter = new TickFilter(maxPriceDeviation);
            _tickFilteringEnabled = true;
        }

        /// <summary>
        /// Gets or sets the target volume size for bars produced by this consolidator.
        /// When setting a new value, if there's a current working bar, its <see cref="VolumeBar.BarSize"/> will be updated.
        /// If the current bar's accumulated volume already meets or exceeds the new BarSize,
        /// it (and any subsequent bars from its excess volume) will be emitted on the next call to <see cref="Update(BaseData)"/>.
        /// </summary>
        public decimal TargetBarSize
        {
            get => _consolidatorTargetBarSize;
            set
            {
                if (value <= 0)
                {
                    throw new ArgumentOutOfRangeException(nameof(value), $"{nameof(TargetBarSize)} must be positive.");
                }

                _consolidatorTargetBarSize = value;
                if (_currentBar != null)
                {
                    // Update the BarSize of the current working bar.
                    // The existing Update() logic in this class, combined with VolumeBar.Update(),
                    // will handle emissions if the current volume now meets or exceeds this new BarSize
                    // upon the next data point arrival.
                    _currentBar.BarSize = _consolidatorTargetBarSize;
                }
            }
        }

        /// <summary>
        /// Updates this consolidator with the specified data
        /// </summary>
        /// <param name="data">The new data for the consolidator</param>
        public override void Update(BaseData data)
        {
            decimal volume;
            decimal open;
            decimal high;
            decimal low;
            decimal close; // Ensure 'close' is assigned in all relevant paths

            switch (data)
            {
                case TradeBar tradeBarInstance: // This will now correctly handle RenkoBar as well
                    volume = tradeBarInstance.Volume;
                    open = tradeBarInstance.Open;
                    high = tradeBarInstance.High;
                    low = tradeBarInstance.Low;
                    close = tradeBarInstance.Close;
                    break;
                case Tick tickInstance:
                    // Only include actual trade information
                    if (tickInstance.TickType != TickType.Trade)
                    {
                        return; // Exit if not a trade tick
                    }

                    volume = tickInstance.Quantity;

                    if (_tickFilteringEnabled)
                    {
                        var effectivePrice = _tickFilter.ProcessTick(tickInstance);
                        open = effectivePrice;
                        high = effectivePrice;
                        low = effectivePrice;
                        close = effectivePrice;
                    }
                    else
                    {
                        // No filtering, use the tick price directly
                        open = tickInstance.Price;
                        high = tickInstance.Price;
                        low = tickInstance.Price;
                        close = tickInstance.Price;
                    }
                    break;
                // No separate RenkoBar case is needed due to BaseRenkoBar inheriting TradeBar.
                default:
                    // Refined error message
                    throw new ArgumentException(
                        $"{GetType().Name} must be used with data that is a TradeBar (or a derived type like RenkoBar) or a Tick. " +
                        $"Received: {data?.GetType().Name ?? "null"}");
            }

            // var adjustedVolume = AdjustVolume(volume, (open + close)/2);
            var adjustedVolume = AdjustVolume(volume, close);

            _currentBar ??= new VolumeBar(data.Symbol, data.Time, data.EndTime, _consolidatorTargetBarSize, open, high, low, close, 0);   // Not setting volume as we're doing that in the Update method
            var volumeLeftOver = _currentBar.Update(data.EndTime, high, low, close, adjustedVolume);

            while (volumeLeftOver > 0)     // We have a full bar and some leftover
            {
                OnDataConsolidated(_currentBar);        // Sending out the full bar
                _currentBar = _currentBar.Rollover();
                volumeLeftOver = _currentBar.Update(data.EndTime, high, low, close, volumeLeftOver);
            }

            if (volumeLeftOver == 0)     // We have a full bar and no leftover
            {
                OnDataConsolidated(_currentBar);        // Sending out the full bar
                _currentBar = null;                     // Resetting the current bar
            }
        }

        /// <summary>
        /// Returns the raw volume without any adjustment.
        /// </summary>
        /// <param name="volume">The volume</param>
        /// <param name="price">The price</param>
        /// <returns>The unmodified volume</returns>
        protected virtual decimal AdjustVolume(decimal volume, decimal price)
        {
            return volume;
        }

        /// <summary>
        /// Scans this consolidator to see if it should emit a bar due to time passing
        /// </summary>
        /// <param name="currentLocalTime">The current time in the local time zone (same as <see cref="BaseData.Time"/>)</param>
        public override void Scan(DateTime currentLocalTime)
        {
        }

        /// <summary>
        /// Resets the consolidator
        /// </summary>
        public override void Reset()
        {
            base.Reset();
            _currentBar = null;
        }

        /// <summary>
        /// Event invocator for the DataConsolidated event. This should be invoked
        /// by derived classes when they have consolidated a new piece of data.
        /// </summary>
        /// <param name="consolidated">The newly consolidated data</param>
        protected void OnDataConsolidated(VolumeBar consolidated)
        {
            base.OnDataConsolidated(consolidated);
            DataConsolidated?.Invoke(this, consolidated);
        }
    }
}
