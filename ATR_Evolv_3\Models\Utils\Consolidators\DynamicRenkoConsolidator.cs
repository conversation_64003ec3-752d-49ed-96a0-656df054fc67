#region imports
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Linq;
    using System.Globalization;
    using System.Drawing;
    using QuantConnect;
    using QuantConnect.Algorithm.Framework;
    using QuantConnect.Algorithm.Framework.Selection;
    using QuantConnect.Algorithm.Framework.Alphas;
    using QuantConnect.Algorithm.Framework.Portfolio;
    using QuantConnect.Algorithm.Framework.Portfolio.SignalExports;
    using QuantConnect.Algorithm.Framework.Execution;
    using QuantConnect.Algorithm.Framework.Risk;
    using QuantConnect.Algorithm.Selection;
    using QuantConnect.Api;
    using QuantConnect.Parameters;
    using QuantConnect.Benchmarks;
    using QuantConnect.Brokerages;
    using QuantConnect.Commands;
    using QuantConnect.Configuration;
    using QuantConnect.Util;
    using QuantConnect.Interfaces;
    using QuantConnect.Algorithm;
    using QuantConnect.Indicators;
    using QuantConnect.Data;
    using QuantConnect.Data.Auxiliary;
    using QuantConnect.Data.Consolidators;
    using QuantConnect.Data.Custom;
    using QuantConnect.Data.Custom.IconicTypes;
    using QuantConnect.DataSource;
    using QuantConnect.Data.Fundamental;
    using QuantConnect.Data.Market;
    using QuantConnect.Data.Shortable;
    using QuantConnect.Data.UniverseSelection;
    using QuantConnect.Notifications;
    using QuantConnect.Orders;
    using QuantConnect.Orders.Fees;
    using QuantConnect.Orders.Fills;
    using QuantConnect.Orders.OptionExercise;
    using QuantConnect.Orders.Slippage;
    using QuantConnect.Orders.TimeInForces;
    using QuantConnect.Python;
    using QuantConnect.Scheduling;
    using QuantConnect.Securities;
    using QuantConnect.Securities.Equity;
    using QuantConnect.Securities.Future;
    using QuantConnect.Securities.Option;
    using QuantConnect.Securities.Positions;
    using QuantConnect.Securities.Forex;
    using QuantConnect.Securities.Crypto;
    using QuantConnect.Securities.CryptoFuture;
    using QuantConnect.Securities.IndexOption;
    using QuantConnect.Securities.Interfaces;
    using QuantConnect.Securities.Volatility;
    using QuantConnect.Storage;
    using QuantConnect.Statistics;
    using QCAlgorithmFramework = QuantConnect.Algorithm.QCAlgorithm;
    using QCAlgorithmFrameworkBridge = QuantConnect.Algorithm.QCAlgorithm;
    using Calendar = QuantConnect.Data.Consolidators.Calendar;
#endregion
/*
 * QUANTCONNECT.COM - Democratizing Finance, Empowering Individuals.
 * Lean Algorithmic Trading Engine v2.0. Copyright 2014 QuantConnect Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
*/

namespace QuantConnect.Data.Consolidators
{    /// <summary>
    /// This consolidator extends RenkoConsolidator to allow dynamic updating of the bar size during runtime.
    /// It maintains the same type (RenkoType.Wicked) and behavior as the base RenkoConsolidator,
    /// but adds the ability to modify the bar size without creating a new consolidator.
    /// </summary>
    public class DynamicRenkoConsolidator : RenkoConsolidator, ITargetBarSizeChangeable
    {
        // Additional event handler for subscribers
        private event EventHandler<RenkoBar> _extraDataConsolidated;

        /// <summary>
        /// Gets or sets the target bar size for Renko bars produced by this consolidator.
        /// When setting a new value, the internal BarSize property is updated.
        /// The new bar size will take effect for future bars and calculations.
        /// </summary>
        public decimal TargetBarSize
        {
            get => BarSize;
            set
            {
                if (value <= 0)
                {
                    throw new ArgumentException("Renko consolidator BarSize must be strictly greater than zero");
                }

                // Update the base class's protected BarSize field
                BarSize = value;
                
                // Note: Any currently in-progress brick calculations will use the new bar size
                // from this point forward
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicRenkoConsolidator"/> class using the specified <paramref name="barSize"/>.
        /// </summary>
        /// <param name="barSize">The initial bar size to use for this consolidator</param>
        public DynamicRenkoConsolidator(decimal barSize)
            : base(barSize)
        {
            // Subscribe to our own DataConsolidated event to fix the SequentialConsolidator issue
            base.DataConsolidated += (sender, consolidated) => 
            {
                // Forward to our extra event
                _extraDataConsolidated?.Invoke(sender, consolidated);
            };
        }

        /// <summary>
        /// Event handler that fires when a new piece of data is produced.
        /// This exists to ensure SequentialConsolidator works correctly.
        /// </summary>
        public new event EventHandler<RenkoBar> DataConsolidated
        {
            add
            {
                base.DataConsolidated += value;
                _extraDataConsolidated += value;
            }
            remove
            {
                base.DataConsolidated -= value;
                _extraDataConsolidated -= value;
            }
        }
    }

    /// <summary>
    /// Provides a type safe wrapper on the DynamicRenkoConsolidator class.
    /// This allows defining selector functions with the real type they'll be receiving.
    /// </summary>
    /// <typeparam name="TInput">The input data type</typeparam>
    public class DynamicRenkoConsolidator<TInput> : DynamicRenkoConsolidator
        where TInput : IBaseData
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="DynamicRenkoConsolidator{TInput}"/> class using the specified <paramref name="barSize"/>.
        /// </summary>
        /// <param name="barSize">The initial bar size to use for this consolidator</param>
        public DynamicRenkoConsolidator(decimal barSize)
            : base(barSize)
        {
        }

        /// <summary>
        /// Updates this consolidator with the specified data.
        /// </summary>
        /// <remarks>
        /// Type safe shim method.
        /// </remarks>
        /// <param name="data">The new data for the consolidator</param>
        public void Update(TInput data)
        {
            base.Update(data);
        }
    }
}
